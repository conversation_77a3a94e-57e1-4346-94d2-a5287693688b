{"BH1750_Interface": {"status_enum": {"BH1750_StatusTypeDef": ["BH1750_OK", "BH1750_ERROR_TIMEOUT", "BH1750_ERROR_I2C", "BH1750_ERROR_NOT_RESPONDING"]}, "functions": [{"name": "bh1750_init", "description": "初始化传感器并配置I2C接口", "return_type": "BH1750_StatusTypeDef", "parameters": [{"name": "i2c_port", "type": "i2c_port_t", "description": "I2C端口号(ESP32对应I2C_NUM_0/I2C_NUM_1)"}, {"name": "sda_pin", "type": "gpio_num_t", "description": "SDA引脚编号"}, {"name": "scl_pin", "type": "gpio_num_t", "description": "SCL引脚编号"}, {"name": "addr_pin_state", "type": "bool", "description": "ADDR引脚电平状态(true=高电平，false=低电平)"}]}, {"name": "bh1750_set_mode", "description": "🚨 CRITICAL: 设置传感器测量模式 - 必须在初始化后立即调用！没有此步骤传感器将返回0或NaN值", "return_type": "BH1750_StatusTypeDef", "parameters": [{"name": "mode", "type": "bh1750_mode_t", "description": "测量模式枚举值 - 推荐使用BH1750_MODE_CONTINUOUS_HIGH_RES"}], "critical_note": "⚠️ 此函数必须在bh1750_init()之后立即调用，否则传感器不会进行测量！"}, {"name": "bh1750_read_lux", "description": "读取光照强度值", "return_type": "float", "parameters": []}, {"name": "bh1750_power_down", "description": "进入低功耗模式", "return_type": "BH1750_StatusTypeDef", "parameters": []}, {"name": "bh1750_reset", "description": "软件复位传感器", "return_type": "BH1750_StatusTypeDef", "parameters": []}], "🚨_CRITICAL_INITIALIZATION_SEQUENCE": {"description": "BH1750传感器必须按照以下顺序初始化，缺少任何步骤都会导致读取失败！", "mandatory_steps": [{"step": 1, "function": "bh1750_init()", "description": "初始化I2C通信和传感器硬件"}, {"step": 2, "function": "bh1750_set_mode(BH1750_MODE_CONTINUOUS_HIGH_RES)", "description": "🔥 CRITICAL: 设置测量模式 - 没有此步骤传感器不会测量！"}, {"step": 3, "function": "delay(180)", "description": "等待第一次测量完成（高分辨率模式需要180ms）"}, {"step": 4, "function": "bh1750_read_lux()", "description": "现在可以正常读取光照强度值"}], "common_mistakes": ["❌ 只调用bh1750_init()而忘记bh1750_set_mode() - 导致读取值为0", "❌ 使用错误的模式常量名称 - 检查头文件中的正确定义", "❌ 没有等待测量完成就立即读取 - 可能得到无效值"]}, "data_types": {"bh1750_mode_t": ["BH1750_MODE_CONTINUOUS_HIGH_RES", "BH1750_MODE_CONTINUOUS_HIGH_RES2", "BH1750_MODE_CONTINUOUS_LOW_RES", "BH1750_MODE_ONE_SHOT_HIGH_RES", "BH1750_MODE_ONE_SHOT_HIGH_RES2", "BH1750_MODE_ONE_SHOT_LOW_RES"], "recommended_mode": "BH1750_MODE_CONTINUOUS_HIGH_RES", "mode_descriptions": {"BH1750_MODE_CONTINUOUS_HIGH_RES": "连续高分辨率模式 - 推荐用于大多数应用", "BH1750_MODE_CONTINUOUS_LOW_RES": "连续低分辨率模式 - 更快但精度较低", "BH1750_MODE_ONE_SHOT_HIGH_RES": "单次高分辨率模式 - 省电但需要手动触发"}}, "hardware_config": {"i2c_frequency": 100000, "default_address": {"ADDR_LOW": "0x23", "ADDR_HIGH": "0x5C"}, "measurement_time": {"HIGH_RES_MODE": 120, "LOW_RES_MODE": 16}}, "error_handling": {"retry_count": 3, "timeout_threshold": 200}, "usage_example": {"description": "正确的BH1750初始化和使用示例", "setup_code": "// 在setup()函数中\nif (bh1750_init(I2C_NUM_0, GPIO_NUM_21, GPIO_NUM_22, false) == BH1750_OK) {\n    // 🚨 CRITICAL: 必须设置测量模式！\n    if (bh1750_set_mode(BH1750_MODE_CONTINUOUS_HIGH_RES) == BH1750_OK) {\n        delay(180); // 等待第一次测量完成\n        Serial.println(\"BH1750 initialized successfully\");\n    } else {\n        Serial.println(\"Failed to set BH1750 mode\");\n    }\n} else {\n    Serial.println(\"Failed to initialize BH1750\");\n}", "loop_code": "// 在loop()函数中\nfloat lux = bh1750_read_lux();\nif (!isnan(lux)) {\n    Serial.printf(\"Light intensity: %.2f lux\\n\", lux);\n} else {\n    Serial.println(\"Failed to read BH1750\");\n}"}}}